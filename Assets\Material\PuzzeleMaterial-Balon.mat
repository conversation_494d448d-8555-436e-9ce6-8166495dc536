%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: PuzzeleMaterial-Balon
  m_Shader: {fileID: 211, guid: 0000000000000000f000000000000000, type: 0}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHATEST_ON
  m_InvalidKeywords:
  - _CFXR_DITHERED_SHADOWS_OFF
  - _CFXR_GLOW_POW_P0
  - _CFXR_OVERLAYBLEND_RGBA
  - _CFXR_OVERLAYTEX_OFF
  - _USEDARK_ON
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses:
  - GRABPASS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortNoise:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DitherCustom:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Flow:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 1099c643b0b224d70939088534f67340, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OverlayTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ScreenDistortionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SecondColorTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Alpha: 1
    - _BacklightTransmittance: 1
    - _Blend2: 1
    - _BlendOp: 0
    - _BlendingType: 0
    - _BumpScale: 1
    - _CFXR_DITHERED_SHADOWS: 0
    - _CFXR_GLOW_POW: 0
    - _CFXR_OVERLAYBLEND: 0
    - _CFXR_OVERLAYTEX: 0
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _ColorMode: 0
    - _Colorpower: 1
    - _Colorrange: 1
    - _Cull: 2
    - _CullMode: 0
    - _Cutoff: 0.654
    - _DebugVisualize: 0
    - _Depthpower: 1
    - _DetailNormalMapScale: 1
    - _DirLightScreenAtten: 1
    - _DirectLightingRamp: 1
    - _DissolveSmooth: 0.1
    - _Distort: 0.1
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionSpeed: 1
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0
    - _Distortionpower: 0.2
    - _DoubleDissolve: 0
    - _DstBlend: 0
    - _EdgeFadePow: 1
    - _Emission: 2
    - _EmissionEnabled: 0
    - _FadeAlongU: 0
    - _FlipbookMode: 0
    - _FresnelPower: 5
    - _Fresnelpower: 3
    - _Fresnelscale: 3
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _GlowMax: 1
    - _GlowMin: 0
    - _HdrBoost: 0
    - _HdrMultiply: 2
    - _IndirectLightingMix: 0.5
    - _InvertDissolveTex: 0
    - _LightingEnabled: 0
    - _LightingWorldPosStrength: 0.2
    - _Mask: 0
    - _Maskpower: 10
    - _MaxValue: 10
    - _Metallic: 0
    - _Mode: 1
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _RingTopOffset: 0.05
    - _ScreenDistortionScale: 0.1
    - _SecondColorSmooth: 0.2
    - _ShadowStrength: 1
    - _Shininess: 0.078125
    - _SingleChannel: 0
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFadeDistanceFar: 1
    - _SoftParticlesFadeDistanceNear: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVDistortionAdd: 0
    - _UVSec: 0
    - _UseAlphaClip: 0
    - _UseBackLighting: 0
    - _UseDissolve: 0
    - _UseDissolveOffsetUV: 0
    - _UseEF: 0
    - _UseEmission: 0
    - _UseFB: 0
    - _UseFontColor: 0
    - _UseLighting: 0
    - _UseLightingWorldPosOffset: 0
    - _UseNormalMap: 0
    - _UseRadialUV: 0
    - _UseSP: 0
    - _UseSecondColor: 0
    - _UseUV2Distortion: 0
    - _UseUVDistortion: 0
    - _Usecenterglow: 0
    - _Usecustomrandom: 0
    - _Usedark: 1
    - _Usedepth: 0
    - _Useonlycolor: 0
    - _WindQuality: 0
    - _WorldSpaceRing: 0
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _CameraFadeParams: {r: 0, g: Infinity, b: 0, a: 0}
    - _Color: {r: 1.6410916, g: 0.22185792, b: 2.017851, a: 1}
    - _ColorAddSubDiff: {r: 1, g: 0, b: 0, a: 0}
    - _DissolveScroll: {r: 0, g: 0, b: 0, a: 0}
    - _DistortScrolling: {r: 0, g: 0, b: 1, a: 1}
    - _DistortionSpeedXYPowerZ: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EndColor: {r: 1, g: 1, b: 0, a: 1}
    - _FresnelColor: {r: 1, g: 1, b: 1, a: 1}
    - _HueVariation: {r: 1, g: 0.5, b: 0, a: 0.1}
    - _OverlayTex_Scroll: {r: 0.1, g: 0.1, b: 1, a: 1}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _SpeedMainTexUVNoiseZW: {r: 0, g: 0, b: 0, a: 0}
    - _StartColor: {r: 1, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
