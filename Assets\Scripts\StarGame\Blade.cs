using UnityEngine;

public class Blade : MonoBehaviour
{
    public float sliceForce = 5f;
    public float minSliceVelocity = 0.01f;
    public Transform particle;
    private Camera mainCamera;
    private TrailRenderer sliceTrail;

    public Vector3 direction { get; private set; }
    public bool slicing { get; private set; }

    private void Awake()
    {
        mainCamera = Camera.main;
        sliceTrail = GetComponentInChildren<TrailRenderer>();
    }

    private void OnEnable()
    {
        StopSlice();
    }

    private void OnDisable()
    {
        StopSlice();
    }

    private void Update()
    {
        if (Input.GetMouseButtonDown(0)) {
            StartSlice();
        } else if (Input.GetMouseButtonUp(0)) {
            StopSlice();
        } else if (slicing) {
            ContinueSlice();
        }
    }

    private void StartSlice()
    {
        Vector3 position = mainCamera.ScreenToWorldPoint(Input.mousePosition);
        position.z = 0f;
        transform.position = position;

        slicing = true;
        sliceTrail.enabled = true;
        sliceTrail.Clear();
    }

    private void StopSlice()
    {
        slicing = false;
        sliceTrail.enabled = false;
    }

    private void ContinueSlice()
    {
        Vector3 newPosition = mainCamera.ScreenToWorldPoint(Input.mousePosition);
        newPosition.z = 0f;
        direction = newPosition - transform.position;
        transform.position = newPosition;

        if (particle != null)
        {
            float distance = Vector3.Distance(particle.position, newPosition);
            float threshold = 0.5f; // belirli bir uzaklık (dilediğiniz gibi ayarlayabilirsiniz)

            if (distance > threshold)
            {
                // Particle'ı kapat
                particle.gameObject.SetActive(false);
                // Konuma taşı
                particle.position = newPosition;
                // Aç
                particle.gameObject.SetActive(true);
            }
            else
            {
                // Takip etmeye devam et
                particle.position = Vector3.Lerp(particle.position, newPosition, Time.deltaTime * 20f);
            }
        }
    }

}
