using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

public class AudioManager : Singleton<AudioManager>
{
    public bool isEnable = true; // Varsay�lan olarak ses a��k
    public List<AudioSource> allAudioSources = new List<AudioSource>();
    public List<AudioSource> audioSourcePool = new List<AudioSource>(); // AudioSource havuzu
    public int initialPoolSize = 10;
    public int maxSimultaneousSounds = 5; // Ayn� anda en fazla ka� ses �alabilir

    private Queue<AudioSource> activeSources = new Queue<AudioSource>(); // �alan sesleri takip eden bir s�ra

    public AudioSource MainSource;
    public AudioSource EffectSource;
    public AudioSource SpecialSource;
    public AudioSource UISource;

    private void Start()
    {
        RefreshAudioSources();
        InitializeAudioSourcePool();
    }

    public void StopSoundButton()
    {
        isEnable = !isEnable;
        UpdateAudioSources();
    }

    private void RefreshAudioSources()
    {
        allAudioSources.Clear();
        allAudioSources.AddRange(FindObjectsOfType<AudioSource>());
        UpdateAudioSources();
    }

    private void UpdateAudioSources()
    {
        foreach (var audioSource in allAudioSources)
        {
            if (audioSource != null)
                audioSource.mute = !isEnable;
        }
    }

    private void InitializeAudioSourcePool()
    {
        for (int i = 0; i < initialPoolSize; i++)
        {
            AudioSource newSource = gameObject.AddComponent<AudioSource>();
            audioSourcePool.Add(newSource);
            allAudioSources.Add(newSource);
        }
    }

    private AudioSource GetAvailableAudioSource()
    {
        // Önce pool'u temizle
        CleanupActiveSources();

        foreach (var source in audioSourcePool)
        {
            if (!source.isPlaying)
            {
                // Ses kaynağını sıfırla
                source.clip = null;
                source.loop = false;
                source.volume = 1.0f;
                return source;
            }
        }

        // Eğer havuzda boş kaynak yoksa, en eski çalan sesi durdurup kullan
        if (activeSources.Count >= maxSimultaneousSounds)
        {
            AudioSource oldestSource = activeSources.Dequeue();
            if (oldestSource != null)
            {
                oldestSource.Stop();
                oldestSource.clip = null;
                oldestSource.loop = false;
                oldestSource.volume = 1.0f;
                return oldestSource;
            }
        }

        // Yeni bir kaynak oluştur (maksimum pool boyutunu kontrol et)
        if (audioSourcePool.Count < initialPoolSize * 2) // Pool boyutunu sınırla
        {
            AudioSource newSource = gameObject.AddComponent<AudioSource>();
            audioSourcePool.Add(newSource);
            allAudioSources.Add(newSource);
            newSource.mute = !isEnable; // Mute durumunu ayarla
            return newSource;
        }

        return null; // Pool dolu ve yeni kaynak oluşturulamıyor
    }

    public void PlaySoundFromPool(string clipName, bool loop)
    {
        if (!isEnable) return;

        AudioClip soundClip = Resources.Load<AudioClip>("Sounds/" + clipName);
        if (soundClip == null) return;

        // Sadece loop olan sesler için çakışma kontrolü yap
        // Kısa efekt sesleri (BubbleBrust gibi) için çakışma kontrolü yapma
        if (loop)
        {
            // Aktif kaynakları temizle (çalmayan kaynakları kaldır)
            CleanupActiveSources();

            foreach (var activeSource in activeSources)
            {
                if (activeSource.clip == soundClip && activeSource.isPlaying)
                {
                    return;
                }
            }
        }

        AudioSource newSource = GetAvailableAudioSource();
        if (newSource == null) return;

        newSource.clip = soundClip;
        newSource.loop = loop;
        newSource.volume = 1.0f; // Ses seviyesini sıfırla

        if (newSource.enabled && newSource.gameObject.activeInHierarchy)
        {
            newSource.Play();

            // Sadece başarıyla çalan sesleri kuyruğa ekle
            if (newSource.isPlaying)
            {
                activeSources.Enqueue(newSource);

                // Loop olmayan sesler için otomatik temizleme
                if (!loop)
                {
                    StartCoroutine(RemoveFromActiveSourcesWhenFinished(newSource));
                }
            }
        }
    }


    public void RegisterNewAudioSource(AudioSource newSource)
    {
        if (!allAudioSources.Contains(newSource))
        {
            allAudioSources.Add(newSource);
            newSource.mute = !isEnable;
        }
    }

    public void StopSound(AudioSource source)
    {
        if (source != null)
        {
            source.Stop();
            source.clip = null;
            CleanupActiveSources(); // Kuyruğu düzgün temizle
        }
    }

    // Çalmayan ses kaynaklarını aktif kuyruktan temizle
    private void CleanupActiveSources()
    {
        Queue<AudioSource> cleanQueue = new Queue<AudioSource>();

        while (activeSources.Count > 0)
        {
            AudioSource source = activeSources.Dequeue();
            if (source != null && source.isPlaying)
            {
                cleanQueue.Enqueue(source);
            }
        }

        activeSources = cleanQueue;
    }

    // Loop olmayan sesler için otomatik temizleme coroutine'i
    private System.Collections.IEnumerator RemoveFromActiveSourcesWhenFinished(AudioSource source)
    {
        if (source == null) yield break;

        // Ses çalmaya başlayana kadar bekle
        yield return new WaitUntil(() => source.isPlaying);

        // Ses bitene kadar bekle
        yield return new WaitUntil(() => !source.isPlaying);

        // Temizleme işlemi
        CleanupActiveSources();
    }

    public void SetSound(string clipName, bool loop, AudioSource source)
    {
        if (!isEnable || source == null) return;
        var soundClip = Resources.Load<AudioClip>("Sounds/" + clipName);
        source.clip = soundClip;
        source.loop = loop;
        source.Play();
    }

    public void StopAllSound()
    {
        foreach (var audioSource in allAudioSources)
        {
            if (audioSource != null)
            {
                audioSource.Stop();
                audioSource.clip = null; // Clip referansını temizle
            }
        }
        activeSources.Clear();
    }
}
